// 收藏项编辑模态组件 - 使用shadcn/ui组件重构

import React, { useState, useEffect } from 'react'
import { Save, AlertCircle, Link, FileText, Tag, Folder, Sparkles, Loader2, Check } from 'lucide-react'

// shadcn/ui组件导入
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useForm, useWatch } from "react-hook-form"
import AITextGenerator from './AITextGenerator'
import AIRecommendations from './AIRecommendations'
import { categoryService } from '../services/categoryService'
import type { Category } from '../types'

interface BookmarkEditModalProps {
  /** 要编辑的收藏项 */
  bookmark: {
    id: string
    title: string
    url: string
    description?: string
    category?: string
    tags?: string[]
  }
  /** 是否显示模态窗口 */
  isOpen: boolean
  /** 保存回调函数 */
  onSave: (updatedBookmark: Partial<BookmarkEditModalProps['bookmark']>) => Promise<void>
  /** 取消回调函数 */
  onCancel: () => void
  /** 是否正在保存 */
  loading?: boolean
}

interface FormData {
  title: string
  url: string
  description: string
  category: string
  tags: string[]
}

/**
 * 收藏项编辑模态组件
 * 提供收藏项的编辑功能，包括标题、URL、描述、分类和标签
 * 使用shadcn/ui组件重构
 */
const BookmarkEditModal: React.FC<BookmarkEditModalProps> = React.memo(({
  bookmark,
  isOpen,
  onSave,
  onCancel,
  loading = false
}) => {
  const [tagInput, setTagInput] = useState('')
  const [showAIRecommendations, setShowAIRecommendations] = useState(false)
  const [aiTagsStatus, setAiTagsStatus] = useState<'idle' | 'loading' | 'fallback' | 'complete' | 'error'>('idle')
  const [categories, setCategories] = useState<Category[]>([])
  const [categoriesLoading, setCategoriesLoading] = useState(false)

  // 使用react-hook-form管理表单状态
  const form = useForm<FormData>({
    defaultValues: {
      title: '',
      url: '',
      description: '',
      category: '默认分类',
      tags: []
    }
  })

  const { watch, setValue, getValues, reset, trigger } = form
  const watchedTags = watch('tags')
  const watchedCategory = useWatch({ control: form.control, name: 'category' })

  // 加载分类数据
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setCategoriesLoading(true)
        const categoriesData = await categoryService.getAllCategories()
        setCategories(categoriesData)
        console.log('📁 BookmarkEditModal加载分类数据:', categoriesData.map(c => c.name))
      } catch (error) {
        console.error('加载分类数据失败:', error)
        // 使用默认分类作为后备
        setCategories([
          { id: '1', name: '默认分类', description: '', color: '#gray', createdAt: new Date(), updatedAt: new Date(), bookmarkCount: 0 }
        ])
      } finally {
        setCategoriesLoading(false)
      }
    }

    loadCategories()
  }, [])

  // 当收藏项数据变化时更新表单
  useEffect(() => {
    if (bookmark) {
      reset({
        title: bookmark.title || '',
        url: bookmark.url || '',
        description: bookmark.description || '',
        category: bookmark.category || '默认分类',
        tags: bookmark.tags || []
      })
    }
  }, [bookmark, reset])

  // 监听AI优化完成消息
  useEffect(() => {
    const handleMessage = (message: any) => {
      // 只有在模态窗口打开时才处理消息
      if (!isOpen) return

      if (message.type === 'AI_TAGS_OPTIMIZED') {
        console.log('BookmarkEditModal收到AI优化完成消息:', message.data)

        // 检查是否是当前请求的优化结果
        const { originalRequest, optimizedResult } = message.data
        const currentTitle = watch('title')
        const currentUrl = watch('url')

        if (originalRequest.title === currentTitle &&
            originalRequest.url === currentUrl) {

          // 更新标签
          const currentTags = getValues('tags') || []
          const newTags = optimizedResult.tags.filter(tag => !currentTags.includes(tag))

          if (newTags.length > 0) {
            setValue('tags', [...currentTags, ...newTags])
            setAiTagsStatus('complete')
            console.log('BookmarkEditModal AI优化完成，已更新标签:', newTags)
          } else {
            setAiTagsStatus('complete')
          }

          // 3秒后自动清除完成状态
          setTimeout(() => {
            setAiTagsStatus('idle')
          }, 3000)
        }
      }
    }

    // 始终添加消息监听器，但在处理器中检查isOpen状态
    chrome.runtime.onMessage.addListener(handleMessage)

    // 清理函数
    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage)
    }
  }, [isOpen, watch, getValues, setValue])

  // 表单验证规则
  const validateTitle = (value: string) => {
    if (!value.trim()) return '标题不能为空'
    if (value.length > 200) return '标题长度不能超过200个字符'
    return true
  }

  const validateUrl = (value: string) => {
    if (!value.trim()) return 'URL不能为空'
    try {
      new URL(value)
      return true
    } catch {
      return '请输入有效的URL地址'
    }
  }

  const validateDescription = (value: string) => {
    if (value && value.length > 1000) return '描述长度不能超过1000个字符'
    return true
  }

  const validateCategory = (value: string) => {
    if (!value.trim()) return '分类不能为空'
    return true
  }

  // 处理表单提交
  const handleSubmit = async (data: FormData) => {
    try {
      await onSave({
        id: bookmark.id,
        title: data.title.trim(),
        url: data.url.trim(),
        description: data.description.trim() || undefined,
        category: data.category.trim(),
        tags: data.tags.filter(tag => tag.trim())
      })
    } catch (error) {
      console.error('保存收藏失败:', error)
      // 这里可以显示错误提示
    }
  }

  // 添加标签
  const handleAddTag = () => {
    const tag = tagInput.trim()
    const currentTags = getValues('tags')
    if (tag && !currentTags.includes(tag)) {
      setValue('tags', [...currentTags, tag])
      setTagInput('')
    }
  }

  // 删除标签
  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = getValues('tags')
    setValue('tags', currentTags.filter(tag => tag !== tagToRemove))
  }

  // 处理标签输入的回车键
  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  // 处理标签输入的键盘事件（包括回车）
  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  // 处理AI推荐的标签选择
  const handleAITagSelect = (tag: string) => {
    const currentTags = getValues('tags')
    if (!currentTags.includes(tag)) {
      setValue('tags', [...currentTags, tag])
    }
  }

  // 处理AI推荐的标签取消选择
  const handleAITagDeselect = (tag: string) => {
    handleRemoveTag(tag)
  }

  // 处理AI推荐的文件夹选择
  const handleAIFolderSelect = (folder: string) => {
    console.log('📁 BookmarkEditModal收到文件夹选择:', {
      folder,
      currentCategory: watchedCategory,
      watchValue: watch('category')
    })
    setValue('category', folder, { shouldDirty: true, shouldTouch: true, shouldValidate: true })
    console.log('📁 BookmarkEditModal文件夹选择后的表单值:', getValues('category'))

    // 强制触发重新渲染
    trigger('category')
  }

  // 处理AI推荐的描述选择
  const handleAIDescriptionSelect = (description: string) => {
    setValue('description', description)
  }

  // 一键接受所有AI推荐
  const handleAcceptAll = (data: { tags: string[], folder?: string, description?: string }) => {
    // 应用推荐的标签
    if (data.tags && data.tags.length > 0) {
      const currentTags = watch('tags') || []
      const newTags = [...new Set([...currentTags, ...data.tags])] // 去重
      setValue('tags', newTags)
    }

    // 应用推荐的文件夹
    if (data.folder) {
      setValue('category', data.folder)
    }

    // 应用生成的描述（如果当前描述为空）
    if (data.description && !watch('description')) {
      setValue('description', data.description)
    }
  }

  // 切换AI推荐显示
  const toggleAIRecommendations = () => {
    setShowAIRecommendations(prev => !prev)
  }

  // AI生成分类
  const handleGenerateCategory = async (onChange: (value: string) => void) => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'AI_GENERATE_CATEGORY',
        data: {
          title: watch('title'),
          url: watch('url'),
          content: watch('description'),
          description: watch('description')
        }
      })

      if (response?.success && response.data?.category) {
        onChange(response.data.category)
      }
    } catch (error) {
      console.error('AI生成分类失败:', error)
    }
  }

  // AI生成标签
  const handleGenerateTags = async () => {
    try {
      setAiTagsStatus('loading')

      const response = await chrome.runtime.sendMessage({
        type: 'AI_GENERATE_TAGS',
        data: {
          title: watch('title'),
          url: watch('url'),
          content: watch('description'),
          description: watch('description'),
          maxTags: 5
        }
      })

      if (response?.success && response.data?.tags) {
        const currentTags = getValues('tags')
        const newTags = response.data.tags.filter((tag: string) => !currentTags.includes(tag))
        if (newTags.length > 0) {
          setValue('tags', [...currentTags, ...newTags])
        }

        // 根据响应类型设置状态
        if (response.data.reasoning?.includes('本地规则')) {
          setAiTagsStatus('fallback')
          console.log(`BookmarkEditModal使用降级策略生成了 ${newTags.length} 个标签:`, newTags)
        } else {
          setAiTagsStatus('complete')
          console.log(`BookmarkEditModal AI生成了 ${newTags.length} 个新标签:`, newTags)
        }
      } else {
        throw new Error(response?.error || 'AI生成失败')
      }
    } catch (error) {
      console.error('AI生成标签失败:', error)
      setAiTagsStatus('error')

      // 3秒后自动清除错误状态
      setTimeout(() => {
        setAiTagsStatus('idle')
      }, 3000)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              编辑收藏
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleAIRecommendations}
              disabled={loading || (!watch('title') && !watch('description') && !watch('url'))}
              className="text-sm"
            >
              <Sparkles className="w-4 h-4 mr-1" />
              {showAIRecommendations ? '隐藏推荐' : '智能推荐'}
            </Button>
          </DialogTitle>
          <DialogDescription>
            编辑收藏项的详细信息，包括标题、网址、描述、分类和标签
          </DialogDescription>
        </DialogHeader>

        {/* AI推荐区域 */}
        {showAIRecommendations && (
          <div className="px-6 pb-4">
            <AIRecommendations
              request={{
                title: watch('title'),
                url: watch('url'),
                content: watch('description'),
                maxRecommendations: 6
              }}
              selectedTags={watchedTags || []}
              selectedFolder={watchedCategory}
              currentDescription={watch('description')}
              onTagSelect={handleAITagSelect}
              onTagDeselect={handleAITagDeselect}
              onFolderSelect={handleAIFolderSelect}
              onDescriptionSelect={handleAIDescriptionSelect}
              onAcceptAll={handleAcceptAll}
              disabled={loading}
              showTagRecommendations={true}
              showFolderRecommendations={true}
              showDescriptionGeneration={true}
              className="bg-muted/30 p-4 rounded-lg"
            />
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* 标题输入 */}
            <FormField
              control={form.control}
              name="title"
              rules={{
                validate: validateTitle
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <FileText className="w-4 h-4 mr-1" />
                    标题 *
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="请输入收藏标题"
                      disabled={loading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* URL输入 */}
            <FormField
              control={form.control}
              name="url"
              rules={{
                validate: validateUrl
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <Link className="w-4 h-4 mr-1" />
                    网址 *
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://example.com"
                      disabled={loading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 描述输入 - 集成AI生成功能 */}
            <FormField
              control={form.control}
              name="description"
              rules={{
                validate: validateDescription
              }}
              render={({ field }) => (
                <FormItem>
                  <AITextGenerator
                    label="描述"
                    placeholder="请输入收藏描述（可选）"
                    value={field.value || ''}
                    onChange={field.onChange}
                    context={{
                      title: watch('title'),
                      url: watch('url'),
                      category: watch('category'),
                      tags: watch('tags')
                    }}
                    generationType="description"
                    disabled={loading}
                    maxRows={3}
                    showSuggestions={true}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 分类选择 */}
            <FormField
              control={form.control}
              name="category"
              rules={{
                validate: validateCategory
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Folder className="w-4 h-4 mr-1" />
                      分类 *
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleGenerateCategory(field.onChange)}
                      disabled={loading || (!watch('title') && !watch('description') && !watch('url'))}
                      className="h-6 px-2 text-xs"
                    >
                      <Sparkles className="w-3 h-3 mr-1" />
                      AI生成
                    </Button>
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择分类" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categoriesLoading ? (
                        <SelectItem value="loading" disabled>加载中...</SelectItem>
                      ) : (
                        <>
                          {/* 确保默认分类存在 */}
                          {!categories.some(cat => cat.name === '默认分类') && (
                            <SelectItem value="默认分类">默认分类</SelectItem>
                          )}
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.name}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 标签输入 */}
            <FormItem>
              <FormLabel className="flex items-center justify-between">
                <div className="flex items-center">
                  <Tag className="w-4 h-4 mr-1" />
                  标签
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleGenerateTags}
                  disabled={loading || aiTagsStatus === 'loading' || (!watch('title') && !watch('description') && !watch('url'))}
                  className="h-6 px-2 text-xs"
                >
                  {aiTagsStatus === 'loading' ? (
                    <>
                      <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                      生成中...
                    </>
                  ) : aiTagsStatus === 'error' ? (
                    <>
                      <AlertCircle className="w-3 h-3 mr-1" />
                      重试
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-3 h-3 mr-1" />
                      AI生成
                    </>
                  )}
                </Button>
              </FormLabel>
              <div className="flex flex-wrap gap-2 mb-2" data-testid="tags-container">
                {watchedTags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="flex items-center gap-1"
                    data-testid={`tag-${tag}`}
                  >
                    {tag}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveTag(tag)}
                      disabled={loading}
                      className="h-auto p-0 w-4 h-4 hover:bg-transparent"
                      aria-label={`删除标签 ${tag}`}
                    >
                      ×
                    </Button>
                  </Badge>
                ))}
              </div>

              {/* AI状态提示 */}
              {aiTagsStatus === 'fallback' && (
                <div className="text-xs text-muted-foreground mb-2 flex items-center">
                  <Loader2 className="w-3 h-3 animate-spin mr-1" />
                  已使用快速生成，AI正在后台优化标签...
                </div>
              )}

              {aiTagsStatus === 'complete' && (
                <div className="text-xs text-green-600 mb-2 flex items-center">
                  <Check className="w-3 h-3 mr-1" />
                  AI标签生成完成
                </div>
              )}

              {aiTagsStatus === 'error' && (
                <div className="text-xs text-red-600 mb-2 flex items-center">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  AI生成失败，请检查网络连接或稍后重试
                </div>
              )}

              <div className="flex">
                <Input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleTagInputKeyPress}
                  onKeyDown={handleTagInputKeyDown}
                  className="rounded-r-none"
                  placeholder="输入标签后按回车添加"
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddTag}
                  disabled={loading || !tagInput.trim()}
                  className="rounded-l-none border-l-0"
                  size="sm"
                >
                  添加
                </Button>
              </div>
            </FormItem>
          </form>
        </Form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="submit"
            disabled={loading}
            onClick={form.handleSubmit(handleSubmit)}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                保存中...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                保存
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
})

// 设置显示名称便于调试
BookmarkEditModal.displayName = 'BookmarkEditModal'

export default BookmarkEditModal

// 导出类型定义
export type { BookmarkEditModalProps }