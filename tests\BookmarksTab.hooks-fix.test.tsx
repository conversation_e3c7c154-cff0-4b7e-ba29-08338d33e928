import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import BookmarksTab from '../src/components/BookmarksTab'
import BookmarksErrorBoundary from '../src/components/BookmarksErrorBoundary'

// Mock Chrome API
const mockChrome = {
  runtime: {
    sendMessage: vi.fn()
  }
}

// @ts-ignore
global.chrome = mockChrome

// Mock hooks
vi.mock('../src/hooks/useViewMode', () => ({
  useViewMode: () => ({
    viewMode: 'card',
    setViewMode: vi.fn(),
    isLoading: false
  })
}))

vi.mock('../src/utils/layoutStability', () => ({
  useViewSwitchStability: () => ({
    containerRef: { current: null },
    displayView: 'card',
    isTransitioning: false
  }),
  useScrollPositionLock: () => ({
    lockScrollPosition: vi.fn()
  })
}))

vi.mock('../src/hooks/useAdvancedSearch', () => ({
  useAdvancedSearch: () => ({
    query: '',
    setQuery: vi.fn(),
    results: [
      { item: { id: '1', title: '测试收藏1', url: 'https://example.com', category: '技术', tags: ['测试'], createdAt: new Date().toISOString() } },
      { item: { id: '2', title: '测试收藏2', url: 'https://example2.com', category: '学习', tags: ['学习'], createdAt: new Date().toISOString() } }
    ],
    suggestions: [],
    isSearching: false,
    hasResults: true,
    totalResults: 2,
    searchTime: 10,
    addFilter: vi.fn(),
    clearFilters: vi.fn()
  })
}))

// Mock components
vi.mock('../src/components/BookmarkEditModal', () => ({
  default: () => null
}))

vi.mock('../src/components/AddBookmarkModal', () => ({
  default: () => null
}))

vi.mock('../src/components/DeleteConfirmModal', () => ({
  default: () => null
}))

vi.mock('../src/components/BookmarkSortSelector', () => ({
  default: () => <div data-testid="sort-selector">排序选择器</div>
}))

vi.mock('../src/components/ViewModeSelector', () => ({
  default: () => <div data-testid="view-mode-selector">视图选择器</div>
}))

vi.mock('../src/components/PaginatedBookmarkList', () => ({
  default: ({ bookmarks }: { bookmarks: any[] }) => (
    <div data-testid="paginated-list">分页列表: {bookmarks.length} 项</div>
  )
}))

describe('BookmarksTab Hooks修复测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockChrome.runtime.sendMessage.mockResolvedValue({
      success: true,
      data: [
        { id: '1', title: '测试收藏1', url: 'https://example.com', category: '技术', tags: ['测试'] },
        { id: '2', title: '测试收藏2', url: 'https://example2.com', category: '学习', tags: ['学习'] }
      ]
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('应该正常渲染而不出现React hooks错误', async () => {
    const { container } = render(
      <BookmarksErrorBoundary>
        <BookmarksTab />
      </BookmarksErrorBoundary>
    )

    // 等待组件加载完成
    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })

    // 验证主要元素存在
    expect(screen.getByText('收藏管理')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('搜索收藏...')).toBeInTheDocument()
    expect(screen.getByTestId('paginated-list')).toBeInTheDocument()

    // 验证没有错误边界被触发
    expect(screen.queryByText('收藏管理页面出现错误')).not.toBeInTheDocument()
  })

  it('应该能够处理多次点击而不出现hooks错误', async () => {
    render(
      <BookmarksErrorBoundary>
        <BookmarksTab />
      </BookmarksErrorBoundary>
    )

    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('搜索收藏...')

    // 模拟多次快速点击和输入
    for (let i = 0; i < 10; i++) {
      fireEvent.click(searchInput)
      fireEvent.change(searchInput, { target: { value: `搜索${i}` } })
    }

    // 验证没有错误
    expect(screen.queryByText('收藏管理页面出现错误')).not.toBeInTheDocument()
  })

  it('应该能够处理搜索状态变化而不出现hooks错误', async () => {
    const mockSetQuery = vi.fn()
    
    // 重新mock useAdvancedSearch以测试状态变化
    vi.mocked(require('../src/hooks/useAdvancedSearch').useAdvancedSearch).mockReturnValue({
      query: '',
      setQuery: mockSetQuery,
      results: [],
      suggestions: [],
      isSearching: false,
      searchTime: 0,
      addFilter: vi.fn(),
      clearFilters: vi.fn()
    })

    render(
      <BookmarksErrorBoundary>
        <BookmarksTab />
      </BookmarksErrorBoundary>
    )

    await waitFor(() => {
      expect(screen.queryByText('加载收藏数据中...')).not.toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('搜索收藏...')
    
    // 测试搜索输入
    fireEvent.change(searchInput, { target: { value: '测试搜索' } })
    
    expect(mockSetQuery).toHaveBeenCalledWith('测试搜索')
    expect(screen.queryByText('收藏管理页面出现错误')).not.toBeInTheDocument()
  })

  it('错误边界应该能够捕获和显示错误', () => {
    // 创建一个会抛出错误的组件
    const ErrorComponent = () => {
      throw new Error('测试错误')
    }

    render(
      <BookmarksErrorBoundary>
        <ErrorComponent />
      </BookmarksErrorBoundary>
    )

    // 验证错误边界显示错误信息
    expect(screen.getByText('收藏管理页面出现错误')).toBeInTheDocument()
    expect(screen.getByText('测试错误')).toBeInTheDocument()
    expect(screen.getByText('重试')).toBeInTheDocument()
  })

  it('错误边界的重试功能应该正常工作', () => {
    let shouldThrow = true
    
    const ConditionalErrorComponent = () => {
      if (shouldThrow) {
        throw new Error('条件错误')
      }
      return <div>组件正常渲染</div>
    }

    const { rerender } = render(
      <BookmarksErrorBoundary>
        <ConditionalErrorComponent />
      </BookmarksErrorBoundary>
    )

    // 验证错误显示
    expect(screen.getByText('收藏管理页面出现错误')).toBeInTheDocument()

    // 修复错误条件
    shouldThrow = false

    // 点击重试按钮
    const retryButton = screen.getByText('重试')
    fireEvent.click(retryButton)

    // 重新渲染组件
    rerender(
      <BookmarksErrorBoundary>
        <ConditionalErrorComponent />
      </BookmarksErrorBoundary>
    )

    // 验证组件正常渲染
    expect(screen.getByText('组件正常渲染')).toBeInTheDocument()
    expect(screen.queryByText('收藏管理页面出现错误')).not.toBeInTheDocument()
  })
})
