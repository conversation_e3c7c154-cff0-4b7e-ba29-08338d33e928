// 分页Hook
// 提供分页逻辑和状态管理

import { useState, useMemo, useEffect, useCallback } from 'react'

export interface PaginationState {
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
}

export interface PaginationResult<T> {
  currentPage: number
  pageSize: number
  totalPages: number
  totalItems: number
  currentItems: T[]
  hasNextPage: boolean
  hasPrevPage: boolean
  goToPage: (page: number) => void
  goToNextPage: () => void
  goToPrevPage: () => void
  goToFirstPage: () => void
  goToLastPage: () => void
  setPageSize: (size: number) => void
}

export interface UsePaginationOptions {
  initialPage?: number
  initialPageSize?: number
  persistKey?: string // 用于持久化页面状态的key
}

/**
 * 分页Hook
 * @param items 要分页的数据数组
 * @param options 分页选项
 * @returns 分页结果和控制方法
 */
export const usePagination = <T>(
  items: T[],
  options: UsePaginationOptions = {}
): PaginationResult<T> => {
  const {
    initialPage = 1,
    initialPageSize = 12,
    persistKey
  } = options

  // 从localStorage恢复状态
  const getInitialState = useCallback(() => {
    if (persistKey) {
      try {
        const saved = localStorage.getItem(`pagination-${persistKey}`)
        if (saved) {
          const state = JSON.parse(saved)
          // 只在5分钟内有效
          if (Date.now() - state.timestamp < 5 * 60 * 1000) {
            return {
              page: Math.max(1, state.page),
              pageSize: state.pageSize || initialPageSize
            }
          }
        }
      } catch (error) {
        console.warn('Failed to restore pagination state:', error)
      }
    }
    return { page: initialPage, pageSize: initialPageSize }
  }, [persistKey, initialPage, initialPageSize])

  const initialState = getInitialState()
  const [currentPage, setCurrentPage] = useState(initialState.page)
  const [pageSize, setPageSizeState] = useState(initialState.pageSize)

  // 计算分页信息
  const paginationInfo = useMemo(() => {
    const totalItems = items.length
    const totalPages = totalItems === 0 ? 0 : Math.ceil(totalItems / pageSize)

    // 确保当前页在有效范围内
    const validCurrentPage = totalPages === 0 ? 1 : Math.min(Math.max(1, currentPage), totalPages)

    const startIndex = (validCurrentPage - 1) * pageSize
    const endIndex = Math.min(startIndex + pageSize, totalItems)
    const currentItems = items.slice(startIndex, endIndex)

    return {
      totalItems,
      totalPages,
      currentPage: validCurrentPage,
      currentItems,
      hasNextPage: validCurrentPage < totalPages,
      hasPrevPage: validCurrentPage > 1
    }
  }, [items, currentPage, pageSize])

  // 保存状态到localStorage
  const saveState = useCallback((page: number, size: number) => {
    if (persistKey) {
      try {
        localStorage.setItem(`pagination-${persistKey}`, JSON.stringify({
          page,
          pageSize: size,
          timestamp: Date.now()
        }))
      } catch (error) {
        console.warn('Failed to save pagination state:', error)
      }
    }
  }, [persistKey])

  // 页面导航方法
  const goToPage = useCallback((page: number) => {
    const validPage = Math.min(Math.max(1, page), paginationInfo.totalPages)
    setCurrentPage(validPage)
    saveState(validPage, pageSize)
  }, [paginationInfo.totalPages, pageSize, saveState])

  const goToNextPage = useCallback(() => {
    if (paginationInfo.hasNextPage) {
      goToPage(currentPage + 1)
    }
  }, [paginationInfo.hasNextPage, currentPage, goToPage])

  const goToPrevPage = useCallback(() => {
    if (paginationInfo.hasPrevPage) {
      goToPage(currentPage - 1)
    }
  }, [paginationInfo.hasPrevPage, currentPage, goToPage])

  const goToFirstPage = useCallback(() => {
    goToPage(1)
  }, [goToPage])

  const goToLastPage = useCallback(() => {
    goToPage(paginationInfo.totalPages)
  }, [goToPage, paginationInfo.totalPages])

  const setPageSize = useCallback((size: number) => {
    const newSize = Math.max(1, size)
    setPageSizeState(newSize)
    
    // 调整当前页以保持大致相同的位置
    const currentIndex = (currentPage - 1) * pageSize
    const newPage = Math.floor(currentIndex / newSize) + 1
    setCurrentPage(newPage)
    
    saveState(newPage, newSize)
  }, [currentPage, pageSize, saveState])

  // 当数据变化时，确保当前页有效
  useEffect(() => {
    if (paginationInfo.totalPages > 0 && currentPage > paginationInfo.totalPages) {
      goToPage(paginationInfo.totalPages)
    }
  }, [paginationInfo.totalPages, currentPage, goToPage])

  return {
    currentPage: paginationInfo.currentPage,
    pageSize,
    totalPages: paginationInfo.totalPages,
    totalItems: paginationInfo.totalItems,
    currentItems: paginationInfo.currentItems,
    hasNextPage: paginationInfo.hasNextPage,
    hasPrevPage: paginationInfo.hasPrevPage,
    goToPage,
    goToNextPage,
    goToPrevPage,
    goToFirstPage,
    goToLastPage,
    setPageSize
  }
}

/**
 * 根据视图模式获取推荐的页面大小
 */
export const getRecommendedPageSize = (viewMode: 'card' | 'compact' | 'row'): number => {
  switch (viewMode) {
    case 'row':
      return 16 // 行视图：每页16个
    case 'compact':
      return 16 // 紧凑视图：每页16个
    case 'card':
      return 10 // 卡片视图：每页10个
    default:
      return 16
  }
}

/**
 * 键盘导航Hook
 */
export const useKeyboardNavigation = (
  onPageChange: (page: number) => void,
  currentPage: number,
  totalPages: number,
  enabled: boolean = true
) => {
  useEffect(() => {
    // 修复React hooks规则违反：不能在useEffect中条件性地早期返回
    // 而是应该在事件处理器中检查enabled状态
    const handleKeyDown = (event: KeyboardEvent) => {
      // 如果未启用键盘导航，直接返回
      if (!enabled) return

      // 只在没有输入框聚焦时响应
      const activeElement = document.activeElement
      if (activeElement?.tagName === 'INPUT' || activeElement?.tagName === 'TEXTAREA') {
        return
      }

      // 检查是否有修饰键按下
      if (event.ctrlKey || event.metaKey || event.altKey || event.shiftKey) {
        return
      }

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault()
          if (currentPage > 1) {
            onPageChange(currentPage - 1)
          }
          break
        case 'ArrowRight':
          event.preventDefault()
          if (currentPage < totalPages) {
            onPageChange(currentPage + 1)
          }
          break
        case 'Home':
          event.preventDefault()
          onPageChange(1)
          break
        case 'End':
          event.preventDefault()
          onPageChange(totalPages)
          break
        case 'PageUp':
          event.preventDefault()
          onPageChange(Math.max(1, currentPage - 5))
          break
        case 'PageDown':
          event.preventDefault()
          onPageChange(Math.min(totalPages, currentPage + 5))
          break
      }
    }

    // 始终添加事件监听器，但在处理器中检查enabled状态
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onPageChange, currentPage, totalPages, enabled])
}
