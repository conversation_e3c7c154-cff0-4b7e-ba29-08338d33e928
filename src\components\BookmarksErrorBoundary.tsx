import React, { Component, ErrorInfo, ReactNode } from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

/**
 * 收藏管理页面错误边界组件
 * 专门用于捕获和处理BookmarksTab组件中的React错误
 */
class BookmarksErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
      errorInfo: null
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('BookmarksTab错误:', error)
    console.error('错误详情:', errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // 可以在这里发送错误报告到监控服务
    this.reportError(error, errorInfo)
  }

  /**
   * 报告错误到控制台（可以扩展为发送到监控服务）
   */
  private reportError(error: Error, errorInfo: ErrorInfo) {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    console.group('🚨 收藏管理页面错误报告')
    console.error('错误信息:', errorReport.message)
    console.error('错误堆栈:', errorReport.stack)
    console.error('组件堆栈:', errorReport.componentStack)
    console.error('发生时间:', errorReport.timestamp)
    console.error('页面URL:', errorReport.url)
    console.error('用户代理:', errorReport.userAgent)
    console.groupEnd()
  }

  /**
   * 重置错误状态，重新渲染组件
   */
  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  /**
   * 刷新页面
   */
  private handleRefresh = () => {
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card className="m-6">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-6 h-6 text-destructive" />
              <div>
                <CardTitle className="text-destructive">收藏管理页面出现错误</CardTitle>
                <CardDescription>
                  页面在渲染过程中遇到了问题，请尝试以下解决方案
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">错误信息：</h4>
              <p className="text-sm text-muted-foreground font-mono">
                {this.state.error?.message || '未知错误'}
              </p>
            </div>

            <div className="flex space-x-3">
              <Button onClick={this.handleReset} variant="default">
                <RefreshCw className="w-4 h-4 mr-2" />
                重试
              </Button>
              <Button onClick={this.handleRefresh} variant="outline">
                刷新页面
              </Button>
            </div>

            <div className="text-xs text-muted-foreground">
              <p>如果问题持续存在，请尝试：</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>清除浏览器缓存</li>
                <li>禁用其他浏览器扩展</li>
                <li>检查浏览器控制台中的详细错误信息</li>
              </ul>
            </div>

            {/* 开发环境下显示详细错误信息 */}
            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm font-medium">
                  开发者信息（点击展开）
                </summary>
                <div className="mt-2 p-3 bg-muted rounded text-xs font-mono whitespace-pre-wrap">
                  <div className="mb-2">
                    <strong>错误堆栈：</strong>
                    <pre>{this.state.error?.stack}</pre>
                  </div>
                  <div>
                    <strong>组件堆栈：</strong>
                    <pre>{this.state.errorInfo.componentStack}</pre>
                  </div>
                </div>
              </details>
            )}
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

export default BookmarksErrorBoundary
